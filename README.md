# Svelte Project 1 - Camera Interaction App with Tailwind CSS

一个使用 SvelteKit 和 Tailwind CSS 构建的相机交互应用程序。

## 🎨 Tailwind CSS 配置

本项目已成功配置并集成了 Tailwind CSS v4，提供现代化的样式解决方案。

### 配置文件

- **tailwind.config.js** - Tailwind CSS 配置文件
- **postcss.config.js** - PostCSS 配置，包含 Tailwind CSS 和 Autoprefixer
- **vite.config.ts** - Vite 配置，包含 Tailwind CSS Vite 插件
- **src/app.css** - 全局样式文件，包含 Tailwind 指令

### 依赖包

```json
{
  "dependencies": {
    "tailwindcss": "^4.1.12",
    "@tailwindcss/vite": "^4.1.12",
    "@tailwindcss/postcss": "^4.1.12"
  }
}
```

## 🚀 开发

安装依赖：

```bash
npm install
```

启动开发服务器：

```bash
npm run dev
```

访问 http://localhost:5174 查看应用程序。

## 🧪 测试 Tailwind CSS

访问 http://localhost:5174/test 查看 Tailwind CSS 测试页面，验证样式是否正常工作。

## 📦 构建

创建生产版本：

```bash
npm run build
```

预览生产构建：

```bash
npm run preview
```

## 🎯 功能特性

- ✅ Tailwind CSS v4 完整集成
- ✅ 响应式设计支持
- ✅ 现代化的实用优先CSS框架
- ✅ 自动样式优化和压缩
- ✅ 开发时热重载支持
- ✅ 生产构建优化

## 📝 使用说明

### 在 Svelte 组件中使用 Tailwind CSS

```svelte
<div class="bg-blue-500 text-white p-4 rounded-lg">
  <h1 class="text-2xl font-bold">Hello Tailwind!</h1>
</div>
```

### 在样式块中使用 @apply

```svelte
<style lang="postcss">
  @reference "tailwindcss";
  .custom-button {
    @apply bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded;
  }
</style>
```

## 🔧 配置详情

项目使用了最新的 Tailwind CSS v4 配置方式：

1. **Vite 插件集成** - 通过 `@tailwindcss/vite` 插件实现无缝集成
2. **PostCSS 处理** - 使用 `@tailwindcss/postcss` 进行样式处理
3. **自动内容检测** - 自动扫描 `./src/**/*.{html,js,svelte,ts}` 文件中的类名
4. **生产优化** - 自动移除未使用的样式，优化最终包大小

## 📚 相关链接

- [Tailwind CSS 官方文档](https://tailwindcss.com/)
- [SvelteKit 官方文档](https://kit.svelte.dev/)
- [Vite 官方文档](https://vitejs.dev/)
