{"architectures": ["Idefics3ForConditionalGeneration"], "image_seq_len": 81, "image_token_id": 49153, "model_type": "idefics3", "scale_factor": 3, "text_config": {"_attn_implementation_autoset": false, "_flash_attn_2_enabled": true, "_name_or_path": "/fsx/m4/experiments/local_experiment_dir/s3_async_temporary_checkpoint_folder/tr_324_opt_400/unwrapped_model", "add_cross_attention": false, "architectures": ["VLlama3ForCausalLM"], "attention_bias": false, "attention_dropout": 0.0, "bad_words_ids": null, "begin_suppress_tokens": null, "bos_token_id": 0, "chunk_size_feed_forward": 0, "cross_attention_hidden_size": null, "decoder_start_token_id": null, "diversity_penalty": 0.0, "do_sample": false, "early_stopping": false, "encoder_no_repeat_ngram_size": 0, "eos_token_id": 0, "exponential_decay_length_penalty": null, "finetuning_task": null, "forced_bos_token_id": null, "forced_eos_token_id": null, "head_dim": 64, "hidden_act": "silu", "hidden_size": 2048, "id2label": {"0": "LABEL_0", "1": "LABEL_1"}, "initializer_range": 0.02, "intermediate_size": 8192, "is_decoder": false, "is_encoder_decoder": false, "label2id": {"LABEL_0": 0, "LABEL_1": 1}, "length_penalty": 1.0, "max_length": 20, "max_position_embeddings": 16384, "min_length": 0, "mlp_bias": false, "model_type": "llama", "neftune_noise_alpha": 0.0, "no_repeat_ngram_size": 0, "num_attention_heads": 32, "num_beam_groups": 1, "num_beams": 1, "num_hidden_layers": 24, "num_key_value_heads": 32, "num_return_sequences": 1, "output_attentions": false, "output_hidden_states": false, "output_scores": false, "pad_token_id": 2, "perceiver_config": {"_attn_implementation_autoset": false, "_name_or_path": "", "add_cross_attention": false, "architectures": null, "attention_dropout": 0.0, "bad_words_ids": null, "begin_suppress_tokens": null, "bos_token_id": null, "chunk_size_feed_forward": 0, "cross_attention_hidden_size": null, "decoder_start_token_id": null, "diversity_penalty": 0.0, "do_sample": false, "early_stopping": false, "encoder_no_repeat_ngram_size": 0, "eos_token_id": null, "exponential_decay_length_penalty": null, "finetuning_task": null, "forced_bos_token_id": null, "forced_eos_token_id": null, "hidden_act": "silu", "id2label": {"0": "LABEL_0", "1": "LABEL_1"}, "is_decoder": false, "is_encoder_decoder": false, "label2id": {"LABEL_0": 0, "LABEL_1": 1}, "length_penalty": 1.0, "max_length": 20, "min_length": 0, "model_type": "vllama3", "no_repeat_ngram_size": 0, "num_beam_groups": 1, "num_beams": 1, "num_key_value_heads": 1, "num_return_sequences": 1, "output_attentions": false, "output_hidden_states": false, "output_scores": false, "pad_token_id": null, "prefix": null, "problem_type": null, "pruned_heads": {}, "qk_layer_norms_perceiver": false, "remove_invalid_values": false, "repetition_penalty": 1.0, "resampler_depth": 6, "resampler_head_dim": 96, "resampler_n_heads": 16, "resampler_n_latents": 64, "return_dict": true, "return_dict_in_generate": false, "sep_token_id": null, "suppress_tokens": null, "task_specific_params": null, "temperature": 1.0, "tf_legacy_loss": false, "tie_encoder_decoder": false, "tie_word_embeddings": true, "tokenizer_class": null, "top_k": 50, "top_p": 1.0, "torch_dtype": null, "torchscript": false, "transformers_version": "4.46.0", "typical_p": 1.0, "use_bfloat16": false}, "prefix": null, "pretraining_tp": 1, "problem_type": null, "pruned_heads": {}, "qk_layer_norms": false, "remove_invalid_values": false, "repetition_penalty": 1.0, "return_dict": true, "return_dict_in_generate": false, "rms_norm_eps": 1e-05, "rope_scaling": null, "rope_theta": 273768.0, "sep_token_id": null, "suppress_tokens": null, "task_specific_params": null, "temperature": 1.0, "tf_legacy_loss": false, "tie_encoder_decoder": false, "tie_word_embeddings": false, "tokenizer_class": null, "top_k": 50, "top_p": 1.0, "torch_dtype": "bfloat16", "torchscript": false, "typical_p": 1.0, "use_bfloat16": false, "use_cache": true, "use_resampler": false, "vocab_size": 49155}, "tie_word_embeddings": false, "torch_dtype": "bfloat16", "transformers_version": "4.46.0", "transformers.js_config": {"kv_cache_dtype": {"q4f16": "float16", "fp16": "float16"}, "dtype": {"embed_tokens": "auto", "vision_encoder": "auto", "decoder_model_merged": "q4"}, "use_external_data_format": {"decoder_model_merged.onnx": true, "decoder_model_merged_fp16.onnx": true}}, "use_cache": true, "vision_config": {"size": {"longest_edge": 1920}, "max_image_size": {"longest_edge": 384}, "_attn_implementation_autoset": false, "_name_or_path": "", "add_cross_attention": false, "architectures": null, "attention_dropout": 0.0, "bad_words_ids": null, "begin_suppress_tokens": null, "bos_token_id": null, "chunk_size_feed_forward": 0, "cross_attention_hidden_size": null, "decoder_start_token_id": null, "diversity_penalty": 0.0, "do_sample": false, "early_stopping": false, "encoder_no_repeat_ngram_size": 0, "eos_token_id": null, "exponential_decay_length_penalty": null, "finetuning_task": null, "forced_bos_token_id": null, "forced_eos_token_id": null, "hidden_act": "gelu_pytorch_tanh", "hidden_size": 1152, "id2label": {"0": "LABEL_0", "1": "LABEL_1"}, "image_size": 384, "initializer_range": 0.02, "intermediate_size": 4304, "is_decoder": false, "is_encoder_decoder": false, "label2id": {"LABEL_0": 0, "LABEL_1": 1}, "layer_norm_eps": 1e-06, "length_penalty": 1.0, "max_length": 20, "min_length": 0, "model_type": "idefics3", "no_repeat_ngram_size": 0, "num_attention_heads": 16, "num_beam_groups": 1, "num_beams": 1, "num_channels": 3, "num_hidden_layers": 27, "num_return_sequences": 1, "output_attentions": false, "output_hidden_states": false, "output_scores": false, "pad_token_id": null, "patch_size": 14, "prefix": null, "problem_type": null, "pruned_heads": {}, "remove_invalid_values": false, "repetition_penalty": 1.0, "return_dict": true, "return_dict_in_generate": false, "sep_token_id": null, "suppress_tokens": null, "task_specific_params": null, "temperature": 1.0, "tf_legacy_loss": false, "tie_encoder_decoder": false, "tie_word_embeddings": false, "tokenizer_class": null, "top_k": 50, "top_p": 1.0, "torch_dtype": null, "torchscript": false, "typical_p": 1.0, "use_bfloat16": false}, "vocab_size": 49155}