{"name": "svelte-projet-1", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch"}, "devDependencies": {"@iconify-json/simple-icons": "^1.2.47", "@iconify-json/solar": "^1.2.4", "@iconify-json/svg-spinners": "^1.2.4", "@sveltejs/adapter-auto": "^6.0.0", "@sveltejs/kit": "^2.22.0", "@sveltejs/vite-plugin-svelte": "^6.0.0", "@vueuse/motion": "^3.0.3", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "typescript": "^5.0.0", "vite": "^7.0.4"}, "dependencies": {"@huggingface/transformers": "^3.7.1", "@rollup/rollup-linux-x64-gnu": "^4.46.3", "@tailwindcss/vite": "^4.1.12", "@xsai-transformers/shared": "^0.0.7", "gpuu": "^1.0.4", "lucide-svelte": "^0.540.0", "pm2": "^6.0.8", "tailwindcss": "^4.1.12"}}