import os
from huggingface_hub import hf_hub_download
from tqdm import tqdm


def download_specific_files(model_id, local_dir, files_to_download):
    """
    Downloads a specific list of files from a Hugging Face model repo
    with a progress bar for each file.
    """
    os.makedirs(local_dir, exist_ok=True)
    print(f"Downloading {len(files_to_download)} files for model '{model_id}' to '{local_dir}'")

    for filename in tqdm(files_to_download, desc="Downloading files"):
        try:
            hf_hub_download(
                repo_id=model_id,
                filename=filename,
                local_dir=local_dir,
                local_dir_use_symlinks=False,  # Set to False to copy files directly
                resume_download=True,
            )
            print(f"Successfully downloaded {filename}")
        except Exception as e:
            print(f"Error downloading {filename}: {e}")


if __name__ == "__main__":
    model_id = "HuggingFaceTB/SmolVLM-Instruct"

    # These are the typical files needed by transformers.js for a quantized model.
    # This list is based on the dtype settings in src/lib/worker.ts
    # and standard transformers.js file naming conventions.
    files_needed = [
        "config.json",
        "generation_config.json",
        "preprocessor_config.json",
        "processor.json",
        "special_tokens_map.json",
        "tokenizer.json",
        "tokenizer_config.json",
        # Quantized model weights
        "model.fp16.q4.safetensors",
        "model.fp16-vision_encoder.q4.safetensors"
    ]

    script_dir = os.path.dirname(os.path.realpath(__file__))
    model_path = os.path.join(script_dir, "static", "models", model_id)

    print(f"Starting selective download of model '{model_id}' to '{model_path}'")
    download_specific_files(model_id, model_path, files_needed)
    print("\nSelective model download complete.")
    print(f"The model is saved in: {model_path}")